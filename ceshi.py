import re
import requests
import logging
from flask import Flask, request, Response

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

# 匹配所有 short-id: 后面的值，不论类型，包上引号
def fix_short_id(yaml_content):
    # 正则允许 key 前后有空格，值为任何非空行
    pat = re.compile(r'^([ \t\-]*short-id\s*:\s*)([^\n\r#]+)', re.MULTILINE)
    def replacer(match):
        key = match.group(1)
        value = match.group(2).strip()
        if not (value.startswith('"') and value.endswith('"')):
            return f'{key}"{value}"'
        return match.group(0)
    return pat.sub(replacer, yaml_content)

@app.route('/convert')
def convert():
    url = request.args.get('url')
    if not url:
        return Response('Missing url param', status=400)

    # 获取客户端的原始 headers
    client_headers = dict(request.headers)

    # 移除 Host header，让 requests 自动设置
    client_headers.pop('Host', None)

    # 移除一些不需要转发的 header
    headers_to_remove = ['Content-Length', 'Transfer-Encoding', 'Connection']
    for h in headers_to_remove:
        client_headers.pop(h, None)

    try:
        logging.debug(f"转发到: {url}")
        logging.debug(f"Headers: {client_headers}")

        # 转发原始 headers 到目标服务器
        resp = requests.get(url, headers=client_headers, timeout=10)
        logging.debug(f"响应状态码: {resp.status_code}")
        resp.raise_for_status()
        config = resp.text
    except requests.exceptions.RequestException as e:
        logging.error(f"请求异常: {str(e)}")
        logging.error(f"请求URL: {url}")
        logging.error(f"请求Headers: {client_headers}")
        return Response('Fetch error: ' + str(e), status=502)

    result = fix_short_id(config)

    # 准备响应对象
    response = Response(result, mimetype='text/yaml; charset=utf-8')

    # 将目标服务器的响应 header 原样返回（除了需要过滤的）
    excluded_headers = {
        'content-encoding',
        'content-length',
        'transfer-encoding',
        'connection',
        'server'
    }

    for header, value in resp.headers.items():
        if header.lower() not in excluded_headers:
            response.headers[header] = value

    return response

if __name__ == '__main__':
    app.run(port=23456)