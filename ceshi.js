const express = require('express');
const axios = require('axios');

const app = express();

// Ensure every short-id value in the YAML is wrapped with double quotes.
function fixShortId(yamlContent) {
  const pattern = /^([\s\-]*short-id\s*:\s*)([^\n\r#]+)/gm;
  return yamlContent.replace(pattern, (match, prefix, rawValue) => {
    const value = rawValue.trim();
    if (value.startsWith('"') && value.endsWith('"')) {
      return match;
    }
    return `${prefix}"${value}"`;
  });
}

app.get('/convert', async (req, res) => {
  const { url } = req.query;
  if (!url) {
    return res.status(400).send('Missing url param');
  }

  const clientHeaders = { ...req.headers };

  delete clientHeaders.host;
  ['content-length', 'transfer-encoding', 'connection'].forEach((header) => {
    delete clientHeaders[header];
  });

  try {
    console.debug(`Forwarding to: ${url}`);
    console.debug(`Headers: ${JSON.stringify(clientHeaders)}`);

    const upstreamResponse = await axios.get(url, {
      headers: clientHeaders,
      timeout: 10000,
      responseType: 'text',
      validateStatus: (status) => status >= 200 && status < 300,
    });

    console.debug(`Response status: ${upstreamResponse.status}`);

    const result = fixShortId(upstreamResponse.data);

    res.set('Content-Type', 'text/yaml; charset=utf-8');

    const excludedHeaders = new Set([
      'content-encoding',
      'content-length',
      'transfer-encoding',
      'connection',
      'server',
    ]);

    Object.entries(upstreamResponse.headers).forEach(([header, value]) => {
      if (!excludedHeaders.has(header.toLowerCase())) {
        res.set(header, value);
      }
    });

    return res.send(result);
  } catch (error) {
    console.error(`Request error: ${error.message}`);
    console.error(`Request URL: ${url}`);
    console.error(`Request Headers: ${JSON.stringify(clientHeaders)}`);

    const status = error.response?.status || 502;
    const statusText = error.response?.statusText || '';
    const message = error.response
      ? `Fetch error: ${status} ${statusText}`.trim()
      : `Fetch error: ${error.message}`;

    return res.status(502).send(message);
  }
});

const PORT = process.env.PORT || 23456;
app.listen(PORT, () => {
  console.log(`Server listening on port ${PORT}`);
});
